import { createResolver } from "nuxt/kit"
import vuetify, { transformAssetUrls } from "vite-plugin-vuetify"
import type { HTTPRequest } from "./types/request"
// Resolve relative from the current file
const resolver = createResolver(import.meta.url)

const endpointDefaults: Partial<HTTPRequest> = {
  credentials: "include",
  baseURL: process.env.NUXT_PUBLIC_BASE_API,
}
// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
  devtools: { enabled: false },

  ssr: false,
  build: {
    transpile: [
      "vuetify",
      // 'pikaday',
      // 'dayjs'
    ],
  },

  app: {
    head: {
      titleTemplate: `%s - ${process.env.NUXT_PUBLIC_APP_NAME}`,
      title: process.env.NUXT_PUBLIC_APP_NAME,
      meta: [
        { charset: "utf-8" },
        { "http-equiv": "x-ua-compatible", content: "IE=edge" },
        { name: "viewport", content: "width=device-width, initial-scale=1.0" },
        { name: "meta:external-link", content: "true" },
      ],
      link: [
        {
          rel: "icon",
          href: `/favicon-2.ico`,
        },
      ],
    },

    baseURL: process.env.NUXT_PUBLIC_APP_BASE_URL,
    cdnURL: process.env.NUXT_PUBLIC_BASE_URL,
  },

  runtimeConfig: {
    ePortalApiUrl: process.env.NUXT_PUBLIC_PORTAL_API_URL,
    baseApi: process.env.NUXT_PUBLIC_BASE_API,
    baseUrl: process.env.NUXT_PUBLIC_APP_BASE_URL,
    appName: process.env.NUXT_PUBLIC_APP_NAME,
    baseRoute: process.env.NUXT_PUBLIC_BASE_ROUTE,
    portalToken: process.env.NUXT_PUBLIC_PORTAL_TOKEN,
    ePortalUrl: process.env.NUXT_PUBLIC_PORTAL_URL,
    pusherAppId: process.env.NUXT_PUBLIC_PUSHER_APP_ID,
    pusherAppKey: process.env.NUXT_PUBLIC_PUSHER_APP_KEY,
    pusherAppSecret: process.env.NUXT_PUBLIC_PUSHER_APP_SECRET,
    portalFrontend: process.env.NUXT_PUBLIC_BASE_URL,
    wsHost: process.env.NUXT_PUBLIC_WS_HOST,
    wsPort: process.env.NUXT_PUBLIC_WS_PORT,
    wssPort: process.env.NUXT_PUBLIC_WSS_PORT,
    ablyKey: process.env.NUXT_PUBLIC_ABLY_KEY,
    pusherAppCluster: process.env.NUXT_PUBLIC_PUSHER_APP_CLUSTER,
    appEnvCheck: process.env.NUXT_PUBLIC_APP_ENV_CHECK,
    nodeEnv: process.env.NUXT_PUBLIC_APP_ENV_CHECK,
    checkEnv: process.env.NUXT_PUBLIC_NODE_ENV,
    authSecret: process.env.AUTH_SECRET,
    passport: {
      baseUrl: process.env.IMIP_AUTH_URL,
      clientId: process.env.IMIP_AUTH_CLIENT_ID,
      clientSecret: process.env.IMIP_AUTH_CLIENT_SECRET,
    },
    public: {
      authPrefix: process.env.NUXT_PUBLIC_AUTH_PREFIX,
      oauthBaseApi: process.env.IMIP_AUTH_URL,
      baseApi: process.env.NUXT_PUBLIC_BASE_API,
      baseUrl: process.env.NUXT_PUBLIC_APP_BASE_URL,
      appName: process.env.NUXT_PUBLIC_APP_NAME,
      baseRoute: process.env.NUXT_PUBLIC_BASE_ROUTE,
      portalToken: process.env.NUXT_PUBLIC_PORTAL_TOKEN,
      ePortalUrl: process.env.NUXT_PUBLIC_PORTAL_URL,
      ePortalApiUrl: process.env.NUXT_PUBLIC_PORTAL_API_URL,
      pusherAppId: process.env.NUXT_PUBLIC_PUSHER_APP_ID,
      pusherAppKey: process.env.NUXT_PUBLIC_PUSHER_APP_KEY,
      pusherAppSecret: process.env.NUXT_PUBLIC_PUSHER_APP_SECRET,
      portalFrontend: process.env.NUXT_PUBLIC_BASE_URL,
      wsHost: process.env.NUXT_PUBLIC_WS_HOST,
      wsPort: process.env.NUXT_PUBLIC_WS_PORT,
      wssPort: process.env.NUXT_PUBLIC_WSS_PORT,
      ablyKey: process.env.NUXT_PUBLIC_ABLY_KEY,
      pusherAppCluster: process.env.NUXT_PUBLIC_PUSHER_APP_CLUSTER,
      appEnvCheck: process.env.NUXT_PUBLIC_APP_ENV_CHECK,
      nodeEnv: process.env.NUXT_PUBLIC_APP_ENV_CHECK,
      checkEnv: process.env.NUXT_PUBLIC_NODE_ENV,
    },
  },

  css: [
    "~/assets/scss/style.scss",
    "@fontsource/public-sans/400.css",
    "@fontsource/public-sans/500.css",
    "@fontsource/public-sans/600.css",
    "@fontsource/public-sans/700.css",
  ],

  modules: [
    // 'dayjs-nuxt',
    "@nuxt-alt/auth",
    "@pinia/nuxt",
    "@nuxt-alt/http",
    "@vueuse/nuxt",
    "@nuxtjs/device",
    "@vite-pwa/nuxt",
    "@nuxtjs/i18n",
    (_options, nuxt) => {
      nuxt.hooks.hook("vite:extendConfig", (config) => {
        // @ts-expect-error
        config.plugins.push(
          vuetify({
            autoImport: true,
            // styles: { configFile: resolve('./assets/scss/settings.scss') },
          }),
        )
      })
    },
    //...
  ],

  i18n: {
    vueI18n: "./i18n.config.ts", // if you are using custom path, default
    strategy: "no_prefix",
    defaultLocale: "en",
    detectBrowserLanguage: false,
    locales: [
      { code: "en", name: "English", iso: "en-US" },
      { code: "zh", name: "Chinese Simplified", iso: "zh-CN" }, // Chinese (Simplified)
    ],
    // lazy: true,
    // langDir: './utils/locales/'
  },

  http: {
    credentials: "include",
    baseURL: process.env.NUXT_PUBLIC_BASE_API,
  },

  auth: {
    localStorage: false,
    sessionStorage: false,
    globalMiddleware: false,
    enableMiddleware: true,
    fullPathRedirect: true,
    redirect: {
      login: "/login",
      logout: "/login",
      callback: "/login",
      home: "/",
    },
    cookie: {
      prefix: process.env.NUXT_PUBLIC_AUTH_PREFIX,
      options: {
        path: process.env.NUXT_PUBLIC_APP_BASE_URL,
      },
    },
    strategies: {
      laravelPassport: {
        provider: "laravel/passport",
        endpoints: {
          authorization: `${process.env.IMIP_AUTH_URL}/oauth/authorize`,
          token: `${process.env.IMIP_AUTH_URL}/oauth/token`,
          logout: `${process.env.IMIP_AUTH_URL}/api/v1/logout`,
          userInfo: `${process.env.IMIP_AUTH_URL}/api/v1/user`,
        },
        token: {
          property: "access_token",
          type: "Bearer",
          maxAge: 7 * 60 * 60,
          httpOnly: false,
        },
        refreshToken: {
          property: "refresh_token",
          maxAge: 7 * 60 * 60 + 30,
        },
        // responseType: 'code',
        scope: "",
        codeChallengeMethod: "plain",
        url: process.env.IMIP_AUTH_URL,
        clientId: process.env.IMIP_AUTH_CLIENT_ID,
        clientSecret: process.env.IMIP_AUTH_CLIENT_SECRET,
      },
      local: {
        cookie: {
          server: false, // by default this is set based on if nuxt ssr is enabled
          name: "access_token",
        },
        // scheme: 'refresh',
        endpoints: {
          csrf: false,
          login: {
            ...endpointDefaults,
            url: "/api/login",
            method: "post",
          },
          logout: {
            ...endpointDefaults,
            url: "/api/auth/logout",
            method: "post",
          },
          user: {
            ...endpointDefaults,
            url: "/api/auth/user",
            method: "post",
          },
        },
        token: {
          property: "token",
          global: true,
          maxAge: 60 * 60 * 8,
          // maxAge: 60 * 60 * 24 * 30 * 12
          // maxAge: 60 * 2
        },
        user: {
          property: "user",
          // property: {
          //   client: true,
          //   server: true
          // },
          autoFetch: true,
        },
      },
    },
  },

  nitro: {
    // preset: 'vercel',
    compressPublicAssets: {
      gzip: true,
      brotli: true,
    },
  },

  vite: {
    vue: {
      template: {
        transformAssetUrls,
      },
    },
    build: {
      sourcemap: process.env.NUXT_PUBLIC_NODE_ENV !== "production",
    },
    clearScreen: true,
    logLevel: "info",
    // plugins: [
    //   ckeditor5({ theme: require.resolve('@ckeditor/ckeditor5-theme-lark') })
    // ],
    optimizeDeps: {
      exclude: [
        // 'date-fns',
        // 'dayjs',
        // 'pikaday'
      ],
      /**
       * This is required only because we use "npm link" for
       * testing purposes. See `dependencies` in `package.json`.
       */
      include: [
        "@ckeditor/ckeditor5-vue",
        // 'dayjs-nuxt@2.1.9',
        // 'dayjs@1.11.10',
        // 'numbro@2.1.2',
        // 'handsontable',
        // '@handsontable/vue3'
      ],
    },
  },

  pwa: {
    workbox: {
      navigateFallback: "/",
      runtimeCaching: [
        {
          urlPattern: /^https:\/\/fonts\.googleapis\.com\/.*/i,
          handler: "CacheFirst",
          options: {
            cacheName: "google-fonts-cache",
            expiration: {
              maxEntries: 10,
              maxAgeSeconds: 60 * 60 * 24 * 365, // <== 365 days
            },
            cacheableResponse: {
              statuses: [0, 200],
            },
          },
        },
        {
          urlPattern: /^https:\/\/fonts\.gstatic\.com\/.*/i,
          handler: "CacheFirst",
          options: {
            cacheName: "gstatic-fonts-cache",
            expiration: {
              maxEntries: 10,
              maxAgeSeconds: 60 * 60 * 24 * 365, // <== 365 days
            },
            cacheableResponse: {
              statuses: [0, 200],
            },
          },
        },
      ],
    },
    registerType: "autoUpdate",
    manifest: {
      name: process.env.NUXT_PUBLIC_APP_NAME,
      short_name: "NuxtVitePWA",
      theme_color: "#ffffff",
      icons: [
        {
          src: "icon-app.png",
          sizes: "192x192",
          type: "image/png",
        },
        {
          src: "icon-app.png",
          sizes: "512x512",
          type: "image/png",
        },
        {
          src: "icon-app.png",
          sizes: "512x512",
          type: "image/png",
          purpose: "any maskable",
        },
      ],
    },
    client: {
      installPrompt: true,
      // you don't need to include this: only for testing purposes
      // if enabling periodic sync for update use 1 hour or so (periodicSyncForUpdates: 3600)
      periodicSyncForUpdates: 20,
    },
    devOptions: {
      enabled: true,
      type: "module",
    },
  },
})
