// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
  // ssr: false,
  devtools: { enabled: false },
  extends: ['../../packages/baseApp'],

  routeRules: {
    '/': { prerender: true, ssr: false },
    '/audit/**': { prerender: true, ssr: false },
    '/auth/**': { prerender: true, ssr: false },
    '/billing/**': { prerender: true, ssr: false },
    '/catalog/**': { prerender: true, ssr: false },
    '/colors/**': { prerender: true, ssr: false },
    '/config/**': { prerender: true, ssr: false },
    '/dashboard/**': { prerender: true, ssr: false },
    // '/excel-upload': { prerender: true, ssr: false },
    '/login/**': { prerender: true, ssr: false },
    '/file/**': { prerender: true, ssr: false },
    '/icon/**': { prerender: true, ssr: false },
    '/kb/**': { prerender: true, ssr: false },
    '/master/**': { prerender: true, ssr: false },
    '/pelabuhan/**': { prerender: true, ssr: false },
    '/report/**': { prerender: true, ssr: false },
    '/user/**': { prerender: true, ssr: false },
    '/workshift/**': { prerender: true, ssr: false },
    '/release-note/**': { prerender: true, ssr: false },
  },

  compatibilityDate: '2024-10-01',
  nitro: {
    moduleSideEffects: ['pspdfkit']
  }
})
